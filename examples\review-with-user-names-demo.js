const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000/api/v1/reviews';
const AUTH_TOKEN = 'your-auth-token-here'; // Replace with actual token

/**
 * Demo: Create a review with user authentication
 */
async function demoCreateReview() {
    console.log('\n=== Create Review Demo ===');
    
    try {
        const reviewData = {
            serviceId: 'SRV_001',
            providerId: 'PRV_001',
            bookingId: 'BKG_001',

            // Category ratings (1-4 scale)
            qualityRating: 4,
            timelinessRating: 3,
            communicationRating: 4,
            valueRating: 3,

            // Legacy rating for backward compatibility
            rating: 4,

            title: 'Great service experience!',
            comment: 'The service was excellent and the staff was very professional. Highly recommended!',

            // Image names from S3 upload
            imageNames: ['reviews/**********-uuid.jpg', 'reviews/**********-uuid.jpg'],

            // Frontend user data (sent from frontend form)
            userName: '<PERSON>',
            userEmail: '<EMAIL>',
            userProfilePicture: 'https://example.com/profiles/john-doe.jpg',

            date: new Date().toISOString()
        };

        const response = await axios.post(BASE_URL, reviewData, {
            headers: {
                'Authorization': `Bearer ${AUTH_TOKEN}`,
                'Content-Type': 'application/json'
            }
        });

        const { review } = response.data;
        
        console.log('Review Created Successfully:');
        console.log(`- Review ID: ${review.reviewId}`);
        console.log(`- 📝 ${review.reviewedBy || `Reviewed by: ${review.displayName}`}`);
        console.log(`- 📧 Email: ${review.displayEmail || 'Not provided'}`);
        console.log(`- 🖼️  Profile Picture: ${review.reviewerInfo?.profilePicture || 'None'}`);
        console.log(`- ${review.isVerifiedReviewer ? '✅ Verified Reviewer' : '❌ Not Verified'}`);
        console.log(`- 👤 User Type: ${review.reviewerType || 'customer'}`);
        console.log(`- 📋 Title: ${review.title}`);
        console.log(`- ⭐ Rating: ${review.rating}/5`);
        console.log(`- 📊 Overall Rating: ${review.overallRating}/4`);
        console.log(`- 💬 Comment: ${review.comment}`);
        console.log(`- 🖼️  Images: ${review.imageNames?.length || 0} uploaded`);
        console.log(`- 📅 Date: ${new Date(review.reviewDate).toLocaleDateString()}`);
        console.log(`\n📋 Frontend Data Received:`);
        console.log(`- Display Name: ${review.displayName}`);
        console.log(`- Display Email: ${review.displayEmail}`);
        console.log(`- Customer Name (legacy): ${review.customerName}`);
        
    } catch (error) {
        console.error('Error creating review:', error.response?.data || error.message);
    }
}

/**
 * Demo: Get reviews with user names
 */
async function demoGetReviews() {
    console.log('\n=== Get Reviews Demo ===');
    
    try {
        const response = await axios.get(`${BASE_URL}?serviceId=SRV_001&limit=5`);
        
        const { reviews, total, page } = response.data;
        
        console.log(`Found ${total} reviews (showing page ${page}):`);
        console.log('');
        
        reviews.forEach((review, index) => {
            console.log(`${index + 1}. Review ${review.reviewId}`);
            console.log(`   📝 Reviewed by: ${review.reviewerInfo?.name || review.customerName || 'Anonymous'}`);
            console.log(`   📧 Email: ${review.reviewerInfo?.email || 'Not provided'}`);
            console.log(`   🖼️  Profile Picture: ${review.reviewerInfo?.profilePicture || 'None'}`);
            console.log(`   ${review.reviewerInfo?.isVerified ? '✅ Verified Reviewer' : '❌ Not Verified'}`);
            console.log(`   👤 User Type: ${review.reviewerInfo?.userType || 'customer'}`);
            console.log(`   📋 Title: ${review.title}`);
            console.log(`   ⭐ Rating: ${review.rating}/5 (Overall: ${review.overallRating}/4)`);
            console.log(`   💬 Comment: ${review.comment.substring(0, 100)}...`);
            console.log(`   📅 Date: ${new Date(review.reviewDate).toLocaleDateString()}`);
            console.log(`   🖼️  Images: ${review.imageUrls?.length || 0}`);
            console.log(`   👍 Helpful Votes: ${review.helpfulVotesCount || 0}`);

            if (review.providerResponse) {
                console.log(`   🏢 Provider Response: ${review.providerResponse.responseText.substring(0, 50)}...`);
            }
            console.log('');
        });
        
    } catch (error) {
        console.error('Error fetching reviews:', error.response?.data || error.message);
    }
}

/**
 * Demo: Get single review by ID with user name
 */
async function demoGetReviewById() {
    console.log('\n=== Get Review by ID Demo ===');
    
    try {
        const reviewId = 'REV_000001'; // Replace with actual review ID
        const response = await axios.get(`${BASE_URL}/${reviewId}`);
        
        const { review } = response.data;
        
        console.log('Review Details:');
        console.log(`- Review ID: ${review.reviewId}`);
        console.log(`- 📝 Reviewed by: ${review.reviewerInfo?.name || review.customerName || 'Anonymous'}`);
        console.log(`- 📧 Reviewer Email: ${review.reviewerInfo?.email || 'Not provided'}`);
        console.log(`- 🖼️  Profile Picture: ${review.reviewerInfo?.profilePicture || 'None'}`);
        console.log(`- ${review.reviewerInfo?.isVerified ? '✅ Verified Reviewer' : '❌ Not Verified'}`);
        console.log(`- 👤 User Type: ${review.reviewerInfo?.userType || 'customer'}`);
        console.log(`- Service ID: ${review.serviceId}`);
        console.log(`- Provider ID: ${review.providerId}`);
        console.log(`- Title: ${review.title}`);
        console.log(`- Rating: ${review.rating}/5`);
        console.log(`- Category Ratings:`);
        console.log(`  * Quality: ${review.qualityRating}/4`);
        console.log(`  * Timeliness: ${review.timelinessRating}/4`);
        console.log(`  * Communication: ${review.communicationRating}/4`);
        console.log(`  * Value: ${review.valueRating}/4`);
        console.log(`- Comment: ${review.comment}`);
        console.log(`- Images: ${review.imageUrls?.length || 0}`);
        console.log(`- Helpful Votes: ${review.helpfulVotesCount || 0} / ${review.totalVotes || 0}`);
        console.log(`- Date: ${new Date(review.reviewDate).toLocaleDateString()}`);
        console.log(`- Verified Purchase: ${review.isVerifiedPurchase ? 'Yes' : 'No'}`);
        
        if (review.providerResponse) {
            console.log(`- Provider Response: ${review.providerResponse.responseText}`);
            console.log(`- Response Date: ${new Date(review.providerResponse.responseDate).toLocaleDateString()}`);
        }
        
    } catch (error) {
        console.error('Error fetching review:', error.response?.data || error.message);
    }
}

/**
 * Demo: Get reviews for frontend with user names
 */
async function demoGetReviewsForFrontend() {
    console.log('\n=== Get Reviews for Frontend Demo ===');
    
    try {
        const response = await axios.get(`${BASE_URL}/frontend`, {
            params: {
                serviceId: 'SRV_001',
                sortBy: 'reviewDate',
                sortOrder: 'desc',
                limit: 3,
                hasImages: true
            }
        });
        
        const { reviews, pagination, filters } = response.data.data;
        
        console.log('Frontend Reviews:');
        console.log(`Total: ${pagination.totalItems} reviews`);
        console.log(`Page: ${pagination.currentPage}/${pagination.totalPages}`);
        console.log(`Applied Filters:`, filters);
        console.log('');
        
        reviews.forEach((review, index) => {
            console.log(`${index + 1}. ${review.title}`);
            console.log(`   📝 Reviewed by: ${review.reviewerInfo?.name || review.customerName || 'Anonymous'}`);
            console.log(`   📧 ${review.reviewerInfo?.email || 'Email not provided'}`);
            console.log(`   🖼️  ${review.reviewerInfo?.profilePicture ? 'Has profile picture' : 'No profile picture'}`);
            console.log(`   ${review.reviewerInfo?.isVerified ? '✅ Verified' : '❌ Not Verified'}`);
            console.log(`   ⭐ Rating: ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)} (${review.rating}/5)`);
            console.log(`   🖼️  Images: ${review.imageUrls?.length || 0}`);
            console.log(`   👍 Helpfulness: ${review.helpfulnessPercentage}% (${review.totalVotes} votes)`);
            console.log(`   📅 Date: ${new Date(review.reviewDate).toLocaleDateString()}`);
            console.log('');
        });
        
    } catch (error) {
        console.error('Error fetching frontend reviews:', error.response?.data || error.message);
    }
}

/**
 * Run all demos
 */
async function runAllDemos() {
    console.log('🎯 Review System with User Names - Demo');
    console.log('==========================================');
    
    // Note: Create review requires authentication
    console.log('\n📝 To create a review, you need a valid authentication token.');
    console.log('Replace AUTH_TOKEN with your actual token and uncomment the line below:');
    console.log('// await demoCreateReview();');
    
    // These demos work without authentication
    await demoGetReviews();
    await demoGetReviewById();
    await demoGetReviewsForFrontend();
    
    console.log('\n✅ Demo completed!');
    console.log('\nKey Features Demonstrated:');
    console.log('- ✅ Complete reviewer information saved in database');
    console.log('- ✅ "Reviewed by: [Reviewer\'s Name]" display');
    console.log('- ✅ Reviewer\'s profile picture URL (when available)');
    console.log('- ✅ Reviewer\'s verification status display');
    console.log('- ✅ Reviewer\'s email (when available)');
    console.log('- ✅ User type identification (customer/provider/admin)');
    console.log('- ✅ Backward compatibility with customerName field');
    console.log('- ✅ Enhanced review creation with user data persistence');
    console.log('- ✅ S3 image URLs and reviewer info in same response');
}

// Run the demo
if (require.main === module) {
    runAllDemos().catch(console.error);
}

module.exports = {
    demoCreateReview,
    demoGetReviews,
    demoGetReviewById,
    demoGetReviewsForFrontend
};
