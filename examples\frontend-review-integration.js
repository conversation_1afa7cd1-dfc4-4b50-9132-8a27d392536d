/**
 * Frontend Review Integration Example
 * 
 * This example shows how to:
 * 1. Send userEmail and userName from frontend to backend
 * 2. Receive enhanced review information with display fields
 * 3. Display reviewer information in the UI
 */

// Example: Creating a review with frontend user data
async function createReviewWithUserData() {
    console.log('\n=== Creating Review with Frontend User Data ===');
    
    // This is the data structure your frontend should send
    const reviewPayload = {
        // Required review fields
        serviceId: 'SRV_001',
        providerId: 'PRV_001',
        bookingId: 'BKG_001',
        
        // Rating information
        rating: 5,
        qualityRating: 4,
        timelinessRating: 5,
        communicationRating: 4,
        valueRating: 5,
        
        // Review content
        title: 'Excellent service experience!',
        comment: 'The service was outstanding from start to finish. Professional team, great communication, and delivered exactly what was promised.',
        
        // Images (if any)
        imageNames: ['reviews/image1.jpg', 'reviews/image2.jpg'],
        
        // 🔥 NEW: Frontend user data (sent from your form/profile)
        userName: '<PERSON>',
        userEmail: '<EMAIL>',
        userProfilePicture: 'https://example.com/profiles/john-doe.jpg',
        
        // Optional: Date
        date: new Date().toISOString()
    };

    try {
        const response = await fetch('/api/v1/reviews', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(reviewPayload)
        });

        const result = await response.json();
        
        if (result.success) {
            console.log('✅ Review created successfully!');
            console.log('📋 Review Data:', result.review);
            
            // 🔥 NEW: Enhanced response with display fields
            console.log('\n📝 Reviewer Information:');
            console.log(`- Display Name: ${result.review.displayName}`);
            console.log(`- Display Email: ${result.review.displayEmail}`);
            console.log(`- Reviewed By Text: ${result.review.reviewedBy}`);
            console.log(`- Has Profile Picture: ${result.review.hasProfilePicture}`);
            console.log(`- Is Verified: ${result.review.isVerifiedReviewer}`);
            console.log(`- Reviewer Type: ${result.review.reviewerType}`);
            
            // Backward compatibility
            console.log(`- Customer Name (legacy): ${result.review.customerName}`);
            
            return result.review;
        } else {
            console.error('❌ Failed to create review:', result.message);
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating review:', error);
        return null;
    }
}

// Example: Fetching reviews with enhanced display information
async function fetchReviewsWithDisplayInfo() {
    console.log('\n=== Fetching Reviews with Display Information ===');
    
    try {
        const response = await fetch('/api/v1/reviews?serviceId=SRV_001&limit=5');
        const result = await response.json();
        
        if (result.success) {
            console.log(`📋 Found ${result.total} reviews:`);
            
            result.reviews.forEach((review, index) => {
                console.log(`\n${index + 1}. Review ${review.reviewId}`);
                
                // 🔥 NEW: Enhanced display information
                console.log(`   📝 ${review.reviewedBy}`);
                console.log(`   📧 Email: ${review.displayEmail || 'Not provided'}`);
                console.log(`   🖼️  Profile Picture: ${review.reviewerInfo?.profilePicture || 'None'}`);
                console.log(`   ${review.isVerifiedReviewer ? '✅ Verified Reviewer' : '❌ Not Verified'}`);
                console.log(`   👤 Type: ${review.reviewerType}`);
                
                // Review content
                console.log(`   📋 Title: ${review.title}`);
                console.log(`   ⭐ Rating: ${review.rating}/5`);
                console.log(`   💬 Comment: ${review.comment.substring(0, 100)}...`);
                console.log(`   📅 Date: ${new Date(review.reviewDate).toLocaleDateString()}`);
                console.log(`   🖼️  Images: ${review.imageUrls?.length || 0}`);
                
                // Provider response (if any)
                if (review.providerResponse) {
                    console.log(`   🏢 Provider Response: ${review.providerResponse.responseText.substring(0, 50)}...`);
                }
            });
            
            return result.reviews;
        } else {
            console.error('❌ Failed to fetch reviews:', result.message);
            return [];
        }
    } catch (error) {
        console.error('❌ Error fetching reviews:', error);
        return [];
    }
}

// Example: React component for displaying review with enhanced info
function ReviewCard({ review }) {
    return `
        <div class="review-card">
            <!-- Reviewer Header -->
            <div class="reviewer-header">
                <div class="reviewer-avatar">
                    ${review.hasProfilePicture 
                        ? `<img src="${review.reviewerInfo.profilePicture}" alt="${review.displayName}" />`
                        : `<div class="avatar-initials">${review.displayName.split(' ').map(n => n[0]).join('')}</div>`
                    }
                </div>
                <div class="reviewer-info">
                    <div class="reviewer-name">${review.reviewedBy}</div>
                    <div class="reviewer-details">
                        <span class="email">${review.displayEmail || 'Email not provided'}</span>
                        ${review.isVerifiedReviewer ? '<span class="verified-badge">✓ Verified</span>' : ''}
                        <span class="user-type-badge">${review.reviewerType}</span>
                    </div>
                </div>
            </div>
            
            <!-- Review Content -->
            <div class="review-content">
                <div class="rating">
                    ${'★'.repeat(review.rating)}${'☆'.repeat(5-review.rating)} (${review.rating}/5)
                </div>
                <h3 class="title">${review.title}</h3>
                <p class="comment">${review.comment}</p>
                
                <!-- Images -->
                ${review.imageUrls?.length > 0 ? `
                    <div class="review-images">
                        ${review.imageUrls.map(url => `<img src="${url}" alt="Review image" />`).join('')}
                    </div>
                ` : ''}
                
                <!-- Footer -->
                <div class="review-footer">
                    <span class="date">${new Date(review.reviewDate).toLocaleDateString()}</span>
                    <span class="helpful">👍 ${review.helpfulVotesCount || 0} helpful</span>
                </div>
            </div>
        </div>
    `;
}

// Example: Frontend form for creating review
function createReviewForm() {
    return `
        <form id="reviewForm" onsubmit="submitReview(event)">
            <!-- Service Information -->
            <input type="hidden" name="serviceId" value="SRV_001" />
            <input type="hidden" name="providerId" value="PRV_001" />
            <input type="hidden" name="bookingId" value="BKG_001" />
            
            <!-- User Information (from logged-in user) -->
            <div class="user-info-section">
                <h3>Your Information</h3>
                <input type="text" name="userName" placeholder="Your Name" required 
                       value="John Doe" />
                <input type="email" name="userEmail" placeholder="Your Email" required 
                       value="<EMAIL>" />
                <input type="url" name="userProfilePicture" placeholder="Profile Picture URL (optional)" 
                       value="https://example.com/profiles/john-doe.jpg" />
            </div>
            
            <!-- Review Content -->
            <div class="review-content-section">
                <h3>Your Review</h3>
                <input type="text" name="title" placeholder="Review Title" required />
                <textarea name="comment" placeholder="Write your review..." required></textarea>
                
                <!-- Ratings -->
                <div class="ratings">
                    <label>Overall Rating: <select name="rating" required>
                        <option value="">Select...</option>
                        <option value="1">1 Star</option>
                        <option value="2">2 Stars</option>
                        <option value="3">3 Stars</option>
                        <option value="4">4 Stars</option>
                        <option value="5">5 Stars</option>
                    </select></label>
                    
                    <label>Quality: <select name="qualityRating">
                        <option value="1">1</option><option value="2">2</option>
                        <option value="3">3</option><option value="4">4</option>
                    </select></label>
                    
                    <label>Communication: <select name="communicationRating">
                        <option value="1">1</option><option value="2">2</option>
                        <option value="3">3</option><option value="4">4</option>
                    </select></label>
                    
                    <label>Value: <select name="valueRating">
                        <option value="1">1</option><option value="2">2</option>
                        <option value="3">3</option><option value="4">4</option>
                    </select></label>
                </div>
                
                <!-- Image Upload -->
                <div class="image-upload">
                    <label>Upload Images (optional):</label>
                    <input type="file" multiple accept="image/*" onchange="handleImageUpload(event)" />
                    <div id="imagePreview"></div>
                </div>
            </div>
            
            <button type="submit">Submit Review</button>
        </form>
    `;
}

// Example: Submit review function
async function submitReview(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const reviewData = Object.fromEntries(formData.entries());
    
    // Add any uploaded image names (from S3 upload)
    reviewData.imageNames = window.uploadedImageNames || [];
    
    console.log('📤 Submitting review with data:', reviewData);
    
    const review = await createReviewWithUserData();
    if (review) {
        console.log('✅ Review submitted successfully!');
        // Redirect or update UI
        window.location.reload();
    }
}

// Example usage
async function runFrontendDemo() {
    console.log('🎯 Frontend Review Integration Demo');
    console.log('=====================================');
    
    // Create a review with frontend user data
    await createReviewWithUserData();
    
    // Fetch and display reviews
    await fetchReviewsWithDisplayInfo();
    
    console.log('\n📋 Key Features:');
    console.log('✅ Frontend sends userName and userEmail');
    console.log('✅ Backend saves user info in database');
    console.log('✅ Enhanced display fields in responses');
    console.log('✅ "Reviewed by: [Name]" format');
    console.log('✅ Profile picture support');
    console.log('✅ Verification status display');
    console.log('✅ Backward compatibility maintained');
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createReviewWithUserData,
        fetchReviewsWithDisplayInfo,
        ReviewCard,
        createReviewForm,
        submitReview,
        runFrontendDemo
    };
}

// Run demo if this file is executed directly
if (typeof window !== 'undefined') {
    // Browser environment
    window.reviewDemo = { runFrontendDemo, createReviewWithUserData, fetchReviewsWithDisplayInfo };
} else if (require.main === module) {
    // Node.js environment
    runFrontendDemo().catch(console.error);
}
