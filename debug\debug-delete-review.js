/**
 * Debug script for review deletion issues
 * 
 * This script helps debug the "Review not found" error when deleting reviews
 */

const mongoose = require('mongoose');
const Review = require('../src/api/v1/Reviews/model/reviewModel');
const logger = require('../src/api/common/utils/logger');

// Database connection
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/gigmosaic-product-mgmt';
        await mongoose.connect(mongoURI);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection error:', error);
        process.exit(1);
    }
};

// Debug function to check review existence and structure
const debugReview = async (reviewId) => {
    try {
        console.log(`\n🔍 Debugging review: ${reviewId}`);
        console.log('=' .repeat(50));

        // Check if review exists (including deleted ones)
        const allReviews = await Review.find({ reviewId });
        console.log(`📊 Total reviews with ID ${reviewId}:`, allReviews.length);

        if (allReviews.length === 0) {
            console.log('❌ No reviews found with this ID');
            
            // Check if there are any reviews at all
            const totalReviews = await Review.countDocuments();
            console.log(`📈 Total reviews in database: ${totalReviews}`);
            
            if (totalReviews > 0) {
                // Show some sample review IDs
                const sampleReviews = await Review.find({}, { reviewId: 1, customerId: 1, isDeleted: 1 }).limit(5);
                console.log('📋 Sample review IDs in database:');
                sampleReviews.forEach(review => {
                    console.log(`  - ${review.reviewId} (customer: ${review.customerId}, deleted: ${review.isDeleted})`);
                });
            }
            return null;
        }

        // Show details of found reviews
        allReviews.forEach((review, index) => {
            console.log(`\n📝 Review ${index + 1}:`);
            console.log(`  - Review ID: ${review.reviewId}`);
            console.log(`  - Customer ID: ${review.customerId}`);
            console.log(`  - Provider ID: ${review.providerId}`);
            console.log(`  - Service ID: ${review.serviceId}`);
            console.log(`  - Is Deleted: ${review.isDeleted}`);
            console.log(`  - Created: ${review.reviewDate}`);
            console.log(`  - Deleted At: ${review.deletedAt || 'N/A'}`);
            console.log(`  - Deleted By: ${review.deletedBy || 'N/A'}`);
        });

        // Check active (non-deleted) reviews
        const activeReview = await Review.findOne({ reviewId, isDeleted: false });
        console.log(`\n✅ Active review found: ${!!activeReview}`);

        return allReviews[0];
    } catch (error) {
        console.error('❌ Error debugging review:', error);
        return null;
    }
};

// Test delete functionality
const testDeleteReview = async (reviewId, userId, userType) => {
    try {
        console.log(`\n🧪 Testing delete review functionality`);
        console.log(`  - Review ID: ${reviewId}`);
        console.log(`  - User ID: ${userId}`);
        console.log(`  - User Type: ${userType}`);

        // Import the service after DB connection
        const ReviewService = require('../src/api/v1/Reviews/service/reviewService');
        
        const result = await ReviewService.deleteReview(reviewId, userId, userType);
        console.log('✅ Delete successful:', result.reviewId);
        return result;
    } catch (error) {
        console.error('❌ Delete failed:', error.message);
        return null;
    }
};

// Create a test review for debugging
const createTestReview = async () => {
    try {
        console.log('\n🔧 Creating test review...');
        
        const ReviewService = require('../src/api/v1/Reviews/service/reviewService');
        
        const testReviewData = {
            serviceId: 'SRV_TEST_001',
            providerId: 'PRV_TEST_001',
            bookingId: 'BKG_TEST_001',
            rating: 5,
            title: 'Test Review for Debugging',
            comment: 'This is a test review created for debugging delete functionality.',
            userName: 'Test User',
            userEmail: '<EMAIL>'
        };

        const testUserId = 'CUST_TEST_001';
        const testUserData = {
            user: {
                userId: testUserId,
                userType: 'customer',
                firstName: 'Test',
                lastName: 'User',
                email: '<EMAIL>'
            }
        };

        const review = await ReviewService.createReview(testReviewData, testUserId, testUserData);
        console.log('✅ Test review created:', review.reviewId);
        return review;
    } catch (error) {
        console.error('❌ Error creating test review:', error.message);
        return null;
    }
};

// Main debug function
const runDebug = async () => {
    console.log('🚀 Review Delete Debug Tool');
    console.log('=' .repeat(50));

    await connectDB();

    // Get command line arguments
    const args = process.argv.slice(2);
    const command = args[0];
    const reviewId = args[1];
    const userId = args[2];
    const userType = args[3] || 'customer';

    switch (command) {
        case 'check':
            if (!reviewId) {
                console.log('❌ Please provide a review ID to check');
                console.log('Usage: node debug-delete-review.js check REV_000001');
                break;
            }
            await debugReview(reviewId);
            break;

        case 'delete':
            if (!reviewId || !userId) {
                console.log('❌ Please provide review ID and user ID');
                console.log('Usage: node debug-delete-review.js delete REV_000001 CUST_001 customer');
                break;
            }
            await debugReview(reviewId);
            await testDeleteReview(reviewId, userId, userType);
            break;

        case 'create':
            const testReview = await createTestReview();
            if (testReview) {
                console.log('\n🔄 Now testing delete on created review...');
                await testDeleteReview(testReview.reviewId, 'CUST_TEST_001', 'customer');
            }
            break;

        case 'list':
            console.log('\n📋 Listing recent reviews...');
            const recentReviews = await Review.find({}, {
                _id: 1,
                reviewId: 1,
                customerId: 1,
                providerId: 1,
                isDeleted: 1,
                reviewDate: 1
            })
            .sort({ reviewDate: -1 })
            .limit(10);

            console.log('Format: [MongoDB _id] | [Custom reviewId] | Customer | Provider | Deleted');
            console.log('-'.repeat(80));
            recentReviews.forEach(review => {
                console.log(`  ${review._id} | ${review.reviewId || 'N/A'} | ${review.customerId} | ${review.providerId} | ${review.isDeleted}`);
            });
            break;

        default:
            console.log('📖 Available commands:');
            console.log('  check <reviewId>                    - Check if review exists');
            console.log('  delete <reviewId> <userId> [type]   - Test delete functionality');
            console.log('  create                              - Create test review and delete it');
            console.log('  list                                - List recent reviews');
            console.log('\nExamples:');
            console.log('  node debug-delete-review.js check REV_000001');
            console.log('  node debug-delete-review.js delete REV_000001 CUST_001 customer');
            console.log('  node debug-delete-review.js create');
            console.log('  node debug-delete-review.js list');
    }

    await mongoose.disconnect();
    console.log('\n✅ Debug session completed');
};

// Handle errors
process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled rejection:', error);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    runDebug().catch(console.error);
}

module.exports = {
    debugReview,
    testDeleteReview,
    createTestReview
};
